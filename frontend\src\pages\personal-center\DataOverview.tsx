import {
  BarChartOutlined,
  CarOutlined,
  UsergroupAddOutlined,
  ExclamationCircleOutlined,
  AlertOutlined,
  TeamOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Spin,
  Grid,
  Flex,
  Typography,
} from 'antd';
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserPersonalStatsResponse } from '@/types/api';

/**
 * 数据概览卡片组件
 *
 * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，
 * 采用响应式网格布局适配不同屏幕尺寸。包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题
 * 2. 显示人员数量统计 - 使用用户组图标，绿色主题
 * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题
 * 4. 显示告警数量统计 - 使用警告图标，红色主题
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 响应式布局特点：
 * - xs (< 576px): 2x2 网格布局，移动端优化
 * - sm (≥ 576px): 2x2 网格布局，小屏设备
 * - md (≥ 768px): 1x4 水平排列，平板优化
 * - lg+ (≥ 992px): 1x4 水平排列，桌面端
 * - 每个统计项都有语义化的图标和颜色主题
 * - 统一的卡片样式和高度
 */
const DataOverview: React.FC = () => {
  /**
   * 响应式检测
   *
   * 使用 Ant Design 的 Grid.useBreakpoint 检测当前屏幕尺寸，
   * 根据不同断点调整统计卡片的布局方式
   */
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  /**
   * 根据屏幕尺寸决定布局方向和列数
   *
   * - xs/sm: 垂直布局，2x2 网格
   * - md+: 水平布局，1x4 排列
   */
  const getLayoutDirection = () => {
    if (screens.md) return 'row';
    return 'column';
  };

  /**
   * 获取统计卡片的响应式样式
   */
  const getCardStyle = () => {
    if (screens.md) {
      // 桌面端：水平排列，等宽分布
      return {
        flex: 1,
        minWidth: 0,
      };
    } else {
      // 移动端：垂直排列，固定高度
      return {
        marginBottom: 12,
      };
    }
  };

  /**
   * 个人统计数据状态管理
   */
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });

  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // 获取统计数据
  useEffect(() => {
    const fetchStatsData = async () => {
      try {
        const stats = await UserService.getUserPersonalStats();
        setPersonalStats(stats);
        setStatsError(null);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStatsError('获取统计数据失败，请稍后重试');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStatsData();
  }, []);

  return (
    <ProCard
      title={
        <Flex align="center" gap={8}>
          <BarChartOutlined style={{ fontSize: 18, color: '#2563eb' }} />
          <span style={{ color: '#1f2937', fontWeight: 600 }}>数据概览</span>
        </Flex>
      }
      style={{
        marginBottom: 16,
        borderRadius: 16,
        border: '1px solid rgba(37, 99, 235, 0.08)',
        background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',
        boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)',
      }}
      headStyle={{
        borderBottom: '1px solid rgba(37, 99, 235, 0.08)',
        paddingBottom: 12,
        background: 'rgba(37, 99, 235, 0.02)',
      }}
      bodyStyle={{
        padding: '16px',
      }}
    >
      {statsError ? (
        <Alert
          message="数据概览加载失败"
          description={statsError}
          type="error"
          showIcon
          style={{
            borderRadius: 8,
          }}
        />
      ) : (
        <Spin spinning={statsLoading}>
          {/* 使用 StatisticCard.Group 组件的响应式布局 */}
          <StatisticCard.Group
            direction={getLayoutDirection()}
            style={{
              gap: screens.md ? 16 : 12,
            }}
          >
            {/* 车辆统计 */}
            <StatisticCard
              style={getCardStyle()}
            >
              <Flex
                align="center"
                justify="flex-start"
                style={{
                  height: '100%',
                  padding: screens.md ? '16px 12px' : '12px 8px',
                  minHeight: screens.md ? 80 : 60,
                }}
              >
                {/* 左侧图标 */}
                <CarOutlined
                  style={{
                    color: '#2563eb',
                    fontSize: screens.md ? 28 : 24,
                    marginRight: screens.md ? 16 : 12,
                  }}
                />
                {/* 右侧标题和数值 */}
                <Flex vertical align="flex-start" justify="center" style={{ flex: 1 }}>
                  <Typography.Text
                    style={{
                      fontSize: screens.md ? 14 : 12,
                      color: '#666',
                      marginBottom: 4,
                    }}
                  >
                    车辆
                  </Typography.Text>
                  <Typography.Text
                    style={{
                      color: '#2563eb',
                      fontSize: screens.md ? 36 : 28,
                      fontWeight: 700,
                      lineHeight: 1,
                    }}
                  >
                    {personalStats.vehicles}
                  </Typography.Text>
                </Flex>
              </Flex>
            </StatisticCard>

            {/* 人员统计 */}
            <StatisticCard
              style={getCardStyle()}
            >
              <Flex
                align="center"
                justify="flex-start"
                style={{
                  height: '100%',
                  padding: screens.md ? '16px 12px' : '12px 8px',
                  minHeight: screens.md ? 80 : 60,
                }}
              >
                {/* 左侧图标 */}
                <UsergroupAddOutlined
                  style={{
                    color: '#059669',
                    fontSize: screens.md ? 28 : 24,
                    marginRight: screens.md ? 16 : 12,
                  }}
                />
                {/* 右侧标题和数值 */}
                <Flex vertical align="flex-start" justify="center" style={{ flex: 1 }}>
                  <Typography.Text
                    style={{
                      fontSize: screens.md ? 14 : 12,
                      color: '#666',
                      marginBottom: 4,
                    }}
                  >
                    人员
                  </Typography.Text>
                  <Typography.Text
                    style={{
                      color: '#059669',
                      fontSize: screens.md ? 36 : 28,
                      fontWeight: 700,
                      lineHeight: 1,
                    }}
                  >
                    {personalStats.personnel}
                  </Typography.Text>
                </Flex>
              </Flex>
            </StatisticCard>

            {/* 预警统计 */}
            <StatisticCard
              style={getCardStyle()}
            >
              <Flex
                align="center"
                justify="flex-start"
                style={{
                  height: '100%',
                  padding: screens.md ? '16px 12px' : '12px 8px',
                  minHeight: screens.md ? 80 : 60,
                }}
              >
                {/* 左侧图标 */}
                <ExclamationCircleOutlined
                  style={{
                    color: '#d97706',
                    fontSize: screens.md ? 28 : 24,
                    marginRight: screens.md ? 16 : 12,
                  }}
                />
                {/* 右侧标题和数值 */}
                <Flex vertical align="flex-start" justify="center" style={{ flex: 1 }}>
                  <Typography.Text
                    style={{
                      fontSize: screens.md ? 14 : 12,
                      color: '#666',
                      marginBottom: 4,
                    }}
                  >
                    预警
                  </Typography.Text>
                  <Typography.Text
                    style={{
                      color: '#d97706',
                      fontSize: screens.md ? 36 : 28,
                      fontWeight: 700,
                      lineHeight: 1,
                    }}
                  >
                    {personalStats.warnings}
                  </Typography.Text>
                </Flex>
              </Flex>
            </StatisticCard>

            {/* 告警统计 */}
            <StatisticCard
              style={getCardStyle()}
            >
              <Flex
                align="center"
                justify="flex-start"
                style={{
                  height: '100%',
                  padding: screens.md ? '16px 12px' : '12px 8px',
                  minHeight: screens.md ? 80 : 60,
                }}
              >
                {/* 左侧图标 */}
                <AlertOutlined
                  style={{
                    color: '#dc2626',
                    fontSize: screens.md ? 28 : 24,
                    marginRight: screens.md ? 16 : 12,
                  }}
                />
                {/* 右侧标题和数值 */}
                <Flex vertical align="flex-start" justify="center" style={{ flex: 1 }}>
                  <Typography.Text
                    style={{
                      fontSize: screens.md ? 14 : 12,
                      color: '#666',
                      marginBottom: 4,
                    }}
                  >
                    告警
                  </Typography.Text>
                  <Typography.Text
                    style={{
                      color: '#dc2626',
                      fontSize: screens.md ? 36 : 28,
                      fontWeight: 700,
                      lineHeight: 1,
                    }}
                  >
                    {personalStats.alerts}
                  </Typography.Text>
                </Flex>
              </Flex>
            </StatisticCard>
          </StatisticCard.Group>
        </Spin>
      )}
    </ProCard>
  );
};

export default DataOverview;
